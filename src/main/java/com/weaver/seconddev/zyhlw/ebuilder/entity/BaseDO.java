package com.weaver.seconddev.zyhlw.ebuilder.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年06月27日 11:00
 */
@Data
@Builder(builderMethodName = "baseDOBuilder")
@NoArgsConstructor
@AllArgsConstructor
public class BaseDO implements Serializable {
    @TableId(type = IdType.NONE)
    private Long id;
}
