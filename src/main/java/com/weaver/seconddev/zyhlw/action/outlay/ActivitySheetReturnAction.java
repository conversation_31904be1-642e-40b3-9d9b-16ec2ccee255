package com.weaver.seconddev.zyhlw.action.outlay;


import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.QueryWrapperSqlUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.weaver.seconddev.zyhlw.util.ConvertDataUtils.convertMapKeyToLowerCase;

/**
 * 活动单回退更新预算单记录预算剩余金额
 * <p>
 * 原E9项目com.zyhlw.web.ActivitySheetReturnAction
 *
 * <AUTHOR>
 * @date 2025年06月26日 13:49
 */
@Service("cmicActivitySheetReturnAction")
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class ActivitySheetReturnAction implements EsbServerlessRpcRemoteInterface {

    private static final String SIMPLE_CLASS_NAME = ActivitySheetReturnAction.class.getSimpleName();


    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("{}: 开始执行活动单回退成功开始计算预算剩余金额", SIMPLE_CLASS_NAME);
        try {
            String userId = MapUtil.getStr(params, "userId");
            if (StringUtils.isEmpty(userId)) {
                return WeaResult.fail("用户id不能为空");
            }
            String requestId = MapUtil.getStr(params, "requestId");
            if (StringUtils.isNotEmpty(requestId)) {
                //关联预算单号
                String orderNo = MapUtil.getStr(params, "guan_lysd");
                //根据关联预算单号获取预算剩余金额
                Float budgetAmount = getBudgetAmount(orderNo);
                //活动总预算金额
                Float activityAmount = MapUtil.getFloat(params, "yu_szje");
                //预算剩余金额 = 预算剩余金额 + 活动总预算金额
                Float newBudgetAmount = budgetAmount + activityAmount;
                //更新预算单记录预算剩余金额
                List<Map<String, Object>> updateDataList = new ArrayList<>();
                Map<String, Object> updateData = new HashMap<>();
                updateData.put("id", orderNo);
                updateData.put("yu_ssyje", newBudgetAmount);
                updateDataList.add(Collections.singletonMap("mainTable", updateData));

                String tableId = dataBaseService.getBaseValue("uf_jing_fysgl", "objId");
                EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder()
                        .userId(userId)
                        .objId(tableId)
                        .datas(updateDataList)
                        .build();
                //调用更新接口
                EBuilderUtil.updateFormDataV2(ebFormDataReq, openPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());

                log.info("{}: 活动单回退成功开始计算预算剩余金额", SIMPLE_CLASS_NAME);

                return WeaResult.success(Collections.singletonMap("success", true));
            }
        } catch (Exception e) {
            log.error("{}: 执行异常", SIMPLE_CLASS_NAME, e);
            return WeaResult.fail("系统异常：" + e.getMessage());
        }
        return WeaResult.success(null);
    }

    /**
     * 获取预算单剩余金额
     *
     * @param orderNo 预算单号
     * @return 预算单剩余金额
     * <AUTHOR>
     * @date 2025年06月26日 13:50
     */
    private Float getBudgetAmount(String orderNo) {
        String sql = "select yu_ssyje from uf_jing_fysgl where id = ?";
        Map<String, Object> data = convertMapKeyToLowerCase(
                dataSqlService.executeCommonSqlOne(sql, SourceType.LOGIC,
                        DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), Collections.singletonList(orderNo)));
        return MapUtil.getFloat(data, "yu_ssyje");

    }

    public static void main(String[] args) {
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntityClass(OutlayBudgetDO.class);
        queryWrapper.eq("id", "100830550000011749");
        queryWrapper.select("yu_ssyje");
        System.out.println(QueryWrapperSqlUtils.extractSql(queryWrapper));
    }
}
